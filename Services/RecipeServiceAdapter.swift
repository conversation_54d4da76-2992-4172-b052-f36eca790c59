import Foundation

struct RecipeServiceAdapter: Sendable {
    let recipeService: RecipeGenerationServiceProtocol
    let pantryService: PantryService
    private let detailPrefetcher: any RecipeDetailPrefetching

    init(recipeService: RecipeGenerationServiceProtocol,
         pantryService: PantryService,
         detailPrefetcher: any RecipeDetailPrefetching = DefaultRecipeDetailPrefetcher()) {
        self.recipeService = recipeService
        self.pantryService = pantryService
        self.detailPrefetcher = detailPrefetcher
    }

    func generate(using request: RecipeGenerationRequest,
                  cookingTimeMinutes: Int,
                  authService: AuthenticationService,
                  prefetchDetails: Bool = true) async throws -> [RecipeUIModel] {
        // Enforce pantry-only (water implicitly allowed by prompt)
        let pantryIngredients = await MainActor.run { pantryService.pantryItems.map { $0.name } }
        guard !pantryIngredients.isEmpty else {
            throw RecipeGenerationError.noPantryItems
        }

        // Build preferences baseline from user profile with Phase 1 constraints
        var preferences: RecipePreferences
        let userPrefs = await MainActor.run { authService.userPreferences }
        if let userPrefs = userPrefs {
            preferences = RecipePreferences(from: userPrefs, cookingTime: cookingTimeMinutes)

            // Phase 1: Apply profile constraints
            // - servings = familySize
            preferences.numberOfServings = userPrefs.familySize

            // - restrictions/allergies/exclusions if respectRestrictions
            if userPrefs.respectRestrictions {
                preferences.dietaryRestrictions = userPrefs.dietaryRestrictions.map { $0.rawValue }
                preferences.allergiesAndIntolerances = userPrefs.allergiesIntolerances.map { $0.rawValue }
                preferences.strictExclusions = userPrefs.strictExclusions.map { $0.rawValue }
                preferences.respectRestrictions = true
            }

            // Phase 2: Apply equipment from profile
            preferences.equipmentOwned = userPrefs.equipmentOwned
        } else {
            preferences = RecipePreferences(
                cookingTimeInMinutes: cookingTimeMinutes,
                numberOfServings: 2,
                dietaryRestrictions: [],
                allergiesAndIntolerances: [],
                strictExclusions: [],
                respectRestrictions: true
            )
        }

        // Overlay request-level preferences (cuisines, additional request, equipment)
        if let reqPrefs = request.requestDetails?.preferences {
            preferences.cuisines = reqPrefs.cuisines
            if let additional = reqPrefs.additionalRequest?.trimmingCharacters(in: .whitespacesAndNewlines), !additional.isEmpty {
                preferences.additionalRequest = additional
            } else {
                preferences.additionalRequest = nil
            }
            preferences.equipmentOwned = reqPrefs.equipmentOwned
        }

        // Build per-meal plan from request (defensively sanitize)
        var perMealPlan: [(meal: MealType, count: Int, maxTime: Int)] = []
        if request.mode == .quick, let details = request.requestDetails, let m = details.meals.first,
           let mealType = MealType(rawValue: m.type) {
            let count = max(1, min(6, m.numberOfDishes))
            let time = max(5, min(120, m.maxCookingTimeMinutes))
            perMealPlan = [(meal: mealType, count: count, maxTime: time)]
        } else if request.mode == .custom, let details = request.requestDetails {
            for meal in details.meals {
                if let mealType = MealType(rawValue: meal.type) {
                    let count = max(1, min(6, meal.numberOfDishes))
                    let time = max(5, min(120, meal.maxCookingTimeMinutes))
                    perMealPlan.append((meal: mealType, count: count, maxTime: time))
                }
            }
        } else {
            // Fallback: single batch using provided cookingTimeMinutes
            let time = max(5, min(120, cookingTimeMinutes))
            perMealPlan = [(meal: .dinner, count: 1, maxTime: time)]
        }

        // Generate ideas per meal with retry + filler to enforce exact counts
        var allModels: [RecipeUIModel] = []
        for plan in perMealPlan {
            var perMealPrefs = preferences
            perMealPrefs.cookingTimeInMinutes = plan.maxTime
            perMealPrefs.targetMealType = plan.meal
            perMealPrefs.targetDishCount = plan.count

            // First attempt (gracefully handle underlying service failures)
            var ui: [RecipeUIModel] = []
            do {
                let ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: perMealPrefs)
                var mapped = ideas.map { mapIdeaToUI($0, pantryIngredients: pantryIngredients, meal: plan.meal) }
                mapped = await filterCompatibleRecipes(mapped, userPreferences: userPrefs)
                ui = mapped
            } catch {
                // If service fails, keep ui empty for retry/fallback below
                ui = []
            }

            // Retry once if short
            if ui.count < plan.count {
                let existingTitles = Set(ui.map { $0.title.lowercased() })
                do {
                    let ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: perMealPrefs)
                    var additional = ideas
                        .map { mapIdeaToUI($0, pantryIngredients: pantryIngredients, meal: plan.meal) }
                        .filter { !existingTitles.contains($0.title.lowercased()) }
                    additional = await filterCompatibleRecipes(additional, userPreferences: userPrefs)
                    ui.append(contentsOf: additional)
                } catch {
                    // Ignore and proceed to filler
                }
            }

            // Filler if still short
            if ui.count < plan.count {
                let needed = plan.count - ui.count
                ui.append(contentsOf: makeFallbacks(count: needed, for: plan.meal))
            }

            allModels.append(contentsOf: ui.prefix(plan.count))
        }

        // Prefetch a few details and cache with stable keys when enabled
        if prefetchDetails {
            await detailPrefetcher.prefetchDetails(for: allModels, pantryIngredients: pantryIngredients, userPreferences: userPrefs)
        }

        return allModels
    }

    // MARK: - Private Helper Methods

    /// Filter recipe ingredients to only include those available in pantry
    private func filterPantryIngredients(_ recipeIngredients: [String], pantryIngredients: [String]) -> [String] {
        let pantrySet = Set(pantryIngredients.map { $0.lowercased() })
        return recipeIngredients.filter { ingredient in
            pantrySet.contains(ingredient.lowercased()) || ingredient.lowercased() == "water"
        }
    }

    /// Phase 1: Post-generation sanity check for profile constraints
    private func filterCompatibleRecipes(_ recipes: [RecipeUIModel], userPreferences: UserPreferences?) async -> [RecipeUIModel] {
        guard let userPrefs = userPreferences, userPrefs.respectRestrictions else {
            return recipes
        }

        // Build blocked terms list
        var blockedTerms: [String] = []
        blockedTerms.append(contentsOf: userPrefs.dietaryRestrictions.map { $0.rawValue })
        blockedTerms.append(contentsOf: userPrefs.allergiesIntolerances.map { $0.rawValue })
        blockedTerms.append(contentsOf: userPrefs.strictExclusions.map { $0.rawValue })
        let blockedTermsSet = Set(blockedTerms.map { $0.lowercased() })

        return recipes.filter { recipe in
            let titleText = recipe.title.lowercased()
            let subtitleText = (recipe.subtitle ?? "").lowercased()
            let ingredientsText = (recipe.ingredientsFromPantry ?? []).joined(separator: " ").lowercased()
            let allText = titleText + " " + subtitleText + " " + ingredientsText

            // Check if any blocked term appears in the recipe text
            for term in blockedTermsSet {
                if allText.contains(term) {
                    return false
                }
            }
            return true
        }
    }

    // MARK: - Phase 3: Regenerate Batch
    func regenerateBatch(_ regen: RegenerateRequest, authService: AuthenticationService) async throws -> [RecipeUIModel] {
        // Build preferences baseline similar to generate(); reuse pantry-only enforcement
        let pantryIngredients = await MainActor.run { pantryService.pantryItems.map { $0.name } }
        guard !pantryIngredients.isEmpty else { throw RecipeGenerationError.noPantryItems }

        var preferences: RecipePreferences
        let userPrefs = await MainActor.run { authService.userPreferences }
        if let userPrefs = userPrefs {
            preferences = RecipePreferences(from: userPrefs, cookingTime: 30)
            preferences.numberOfServings = userPrefs.familySize
            if userPrefs.respectRestrictions {
                preferences.dietaryRestrictions = userPrefs.dietaryRestrictions.map { $0.rawValue }
                preferences.allergiesAndIntolerances = userPrefs.allergiesIntolerances.map { $0.rawValue }
                preferences.strictExclusions = userPrefs.strictExclusions.map { $0.rawValue }
                preferences.respectRestrictions = true
            }
            // Phase 2: Apply equipment from profile
            preferences.equipmentOwned = userPrefs.equipmentOwned
        } else {
            preferences = RecipePreferences(cookingTimeInMinutes: 30, numberOfServings: 2, dietaryRestrictions: [])
        }
        preferences.cuisines = regen.cuisines
        preferences.additionalRequest = regen.additionalRequest

        // First attempt
        var ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: preferences)
        var replacements = await mapIdeasToReplacements(ideas: ideas, exclusions: regen.exclusions.titles, needed: regen.targetSlots.count)

        // Retry once if fewer than needed
        if replacements.count < regen.targetSlots.count {
            ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: preferences)
            let additional = await mapIdeasToReplacements(ideas: ideas, exclusions: regen.exclusions.titles + replacements.map { $0.title }, needed: regen.targetSlots.count - replacements.count)
            replacements.append(contentsOf: additional)
        }

        return replacements
    }

    // Simple similarity-based filtering and mapping
    @MainActor
    private func mapIdeasToReplacements(ideas: [RecipeIdea], exclusions: [String], needed: Int) -> [RecipeUIModel] {
        let excluded = exclusions.map { $0.lowercased() }
        var results: [RecipeUIModel] = []
        for idea in ideas {
            let r = idea.recipe
            let title = r.title
            if excluded.contains(where: { similar($0, title.lowercased()) >= 0.85 }) { continue }
            let model = RecipeUIModel(
                id: r.id.uuidString,
                title: r.title,
                subtitle: r.description,
                estimatedTime: r.cookingTimeInMinutes,
                imageURL: nil,
                ingredientsFromPantry: filterPantryIngredients(r.ingredients, pantryIngredients: pantryService.pantryItems.map { $0.name }),
                additionalIngredients: [],
                difficulty: r.difficulty.rawValue
            )
            results.append(model)
            if results.count >= needed { break }
        }
        return results
    }

    // Normalized token similarity (very simple Jaccard index)
    private func similar(_ a: String, _ b: String) -> Double {
        let tokensA = Set(a.split(separator: " ").map { String($0) })
        let tokensB = Set(b.split(separator: " ").map { String($0) })
        let inter = tokensA.intersection(tokensB).count
        let union = tokensA.union(tokensB).count
        if union == 0 { return 0 }
        return Double(inter) / Double(union)
    }
}

// MARK: - Adapter Helpers (Per-Meal Mapping)

extension RecipeServiceAdapter {
    private func mapIdeaToUI(_ idea: RecipeIdea, pantryIngredients: [String], meal: MealType) -> RecipeUIModel {
        let r = idea.recipe
        return RecipeUIModel(
            id: r.id.uuidString,
            title: r.title,
            subtitle: r.description,
            estimatedTime: r.cookingTimeInMinutes,
            imageURL: nil,
            ingredientsFromPantry: filterPantryIngredients(r.ingredients, pantryIngredients: pantryIngredients),
            additionalIngredients: [],
            difficulty: r.difficulty.rawValue,
            mealType: meal,
            dayIndex: nil,
            servings: r.servings,
            cuisine: nil
        )
    }

    private func makeFallbacks(count: Int, for meal: MealType) -> [RecipeUIModel] {
        guard count > 0 else { return [] }
        return (0..<count).map { idx in
            let title = "Pantry \(meal.displayName) Idea \(idx + 1)"
            return RecipeUIModel(
                id: UUID().uuidString,
                title: title,
                subtitle: "Quick idea based on your pantry",
                estimatedTime: 30,
                imageURL: nil,
                ingredientsFromPantry: [],
                additionalIngredients: [],
                difficulty: "easy",
                mealType: meal,
                dayIndex: nil,
                servings: 2,
                cuisine: nil
            )
        }
    }

}

// MARK: - Detail Prefetcher Abstraction

protocol RecipeDetailPrefetching: Sendable {
    func prefetchDetails(for models: [RecipeUIModel], pantryIngredients: [String], userPreferences: UserPreferences?) async
}

struct DefaultRecipeDetailPrefetcher: RecipeDetailPrefetching {
    func prefetchDetails(for models: [RecipeUIModel], pantryIngredients: [String], userPreferences: UserPreferences?) async {
        // Prefetch top K to improve UX; keep small to control cost
        let K = min(3, models.count)
        guard K > 0 else { return }

        // Build preferences baseline similar to generate()
        let prefs: RecipePreferences
        if let user = userPreferences {
            var p = RecipePreferences(from: user, cookingTime: 30)
            p.cuisines = []
            p.additionalRequest = nil
            p.equipmentOwned = user.equipmentOwned
            prefs = p
        } else {
            prefs = RecipePreferences(
                cookingTimeInMinutes: 30,
                numberOfServings: 2,
                dietaryRestrictions: [],
                allergiesAndIntolerances: [],
                strictExclusions: [],
                respectRestrictions: true
            )
        }

        let cache = await RecipeDetailCache.shared
        let mealSpecHash: String? = nil // Not required for detail view prefetch

        await withTaskGroup(of: Void.self) { group in
            for model in models.prefix(K) {
                let title = model.title
                let cuisine = model.cuisine
                let equipmentOwned = userPreferences?.equipmentOwned ?? []

                group.addTask { @Sendable in
                    // Skip if cached
                    if await cache.getCachedDetail(forTitle: title, pantryNames: pantryIngredients, preferences: prefs, mealSpecHash: mealSpecHash) != nil {
                        return
                    }
                    // Fetch and cache
                    do {
                        let detail = try await GeminiAPIService().generateRecipeDetail(
                            title: title,
                            pantryContext: nil,
                            cuisines: cuisine.map { [$0] } ?? [],
                            equipmentOwned: equipmentOwned
                        )
                        await cache.cacheDetail(detail, forTitle: title, pantryNames: pantryIngredients, preferences: prefs, mealSpecHash: mealSpecHash)
                    } catch {
                        // Best-effort prefetch; ignore errors
                    }
                }
            }
        }
    }
}
