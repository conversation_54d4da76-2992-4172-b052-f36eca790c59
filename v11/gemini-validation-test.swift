#!/usr/bin/env swift

import Foundation

// Simple validation script to test Gemini 2.0 Flash API
struct GeminiValidationTest {
    private let apiKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
    
    func runValidation() async {
        print("🚀 Starting Gemini 2.5 Flash Validation Test")
        print("📍 Testing endpoint: \(baseURL)")
        
        do {
            // Test 1: Basic API connectivity
            print("\n✅ Test 1: Basic API Connectivity")
            let response1 = try await testBasicConnectivity()
            print("   ✓ API responded successfully")
            print("   ✓ Response length: \(response1.count) characters")
            
            // Test 2: Recipe generation (core functionality)
            print("\n✅ Test 2: Recipe Generation")
            let response2 = try await testRecipeGeneration()
            print("   ✓ Recipe generation successful")
            print("   ✓ Response contains recipe data")
            
            // Test 3: Parameter validation (no topK)
            print("\n✅ Test 3: Parameter Validation")
            let response3 = try await testParameterValidation()
            print("   ✓ Request without topK parameter successful")
            
            print("\n🎉 All tests passed! Gemini 2.5 Flash migration is working correctly.")
            
        } catch {
            print("\n❌ Test failed: \(error)")
            exit(1)
        }
    }
    
    private func testBasicConnectivity() async throws -> String {
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Hello, can you respond with 'API Working'?"]
                    ]
                ]
            ]
        ]
        
        return try await makeRequest(requestBody: requestBody)
    }
    
    private func testRecipeGeneration() async throws -> String {
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Generate a simple recipe using chicken and rice. Return only the recipe title."]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topP": 0.95,
                "maxOutputTokens": 100
            ]
        ]
        
        return try await makeRequest(requestBody: requestBody)
    }
    
    private func testParameterValidation() async throws -> String {
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Say 'Parameters OK' if you received this request."]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.5,
                "topP": 0.9,
                "maxOutputTokens": 50
                // Note: No topK parameter - this should work with Gemini 2.5
            ]
        ]
        
        return try await makeRequest(requestBody: requestBody)
    }
    
    private func makeRequest(requestBody: [String: Any]) async throws -> String {
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw ValidationError.invalidURL
        }
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ValidationError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            let errorBody = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw ValidationError.apiError(statusCode: httpResponse.statusCode, body: errorBody)
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw ValidationError.invalidResponseFormat
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

enum ValidationError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case invalidResponseFormat
    case apiError(statusCode: Int, body: String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid HTTP response"
        case .invalidResponseFormat:
            return "Invalid response format from Gemini API"
        case .apiError(let statusCode, let body):
            return "API Error \(statusCode): \(body)"
        }
    }
}

// Run the validation
Task {
    let validator = GeminiValidationTest()
    await validator.runValidation()
}

// Keep the script running
RunLoop.main.run()
