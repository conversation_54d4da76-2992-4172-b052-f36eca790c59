#!/usr/bin/env swift

import Foundation

// Integration test for core app functionality
struct IntegrationTest {
    private let geminiAPIKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    private let visionAPIKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM" // Same key for both services
    
    func runTests() async {
        print("🧪 Starting Integration Tests")
        print("=" * 50)
        
        do {
            // Test 1: Gemini API (Recipe Generation)
            print("\n📝 Test 1: Recipe Generation Service")
            try await testRecipeGeneration()
            
            // Test 2: Google Vision API (Image Recognition)
            print("\n👁️ Test 2: Image Recognition Service")
            try await testImageRecognition()
            
            // Test 3: End-to-End Flow Simulation
            print("\n🔄 Test 3: End-to-End Flow")
            try await testEndToEndFlow()
            
            print("\n🎉 All integration tests passed!")
            print("✅ 餐单生成功能正常")
            print("✅ 图片识别功能正常")
            
        } catch {
            print("\n❌ Integration test failed: \(error)")
            exit(1)
        }
    }
    
    // Test recipe generation with Gemini 2.5 Flash
    private func testRecipeGeneration() async throws {
        let ingredients = ["chicken", "rice", "vegetables"]
        let prompt = """
        Generate 2 simple recipes using these ingredients: \(ingredients.joined(separator: ", ")).
        Return ONLY a JSON array with this exact format:
        [
          {
            "title": "Recipe Name",
            "description": "Brief description",
            "ingredients": ["ingredient1", "ingredient2"],
            "instructions": ["step1", "step2"],
            "nutrition": {"calories": 400, "protein": 25, "carbs": 45, "fat": 12},
            "cookingTime": 30,
            "servings": 2,
            "difficulty": "easy"
          }
        ]
        """
        
        let response = try await callGeminiAPI(prompt: prompt)
        
        // Validate response contains JSON
        guard response.contains("[") && response.contains("]") else {
            throw TestError.invalidResponse("Response doesn't contain JSON array")
        }
        
        print("   ✓ Gemini API responded with recipe data")
        print("   ✓ Response length: \(response.count) characters")
        print("   ✓ Contains JSON structure: ✓")
    }
    
    // Test Google Vision API with a simple request
    private func testImageRecognition() async throws {
        // Create a simple test image (1x1 white pixel as base64)
        let testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        
        let requestBody: [String: Any] = [
            "requests": [
                [
                    "image": [
                        "content": testImageBase64
                    ],
                    "features": [
                        ["type": "TEXT_DETECTION", "maxResults": 10],
                        ["type": "LABEL_DETECTION", "maxResults": 10]
                    ]
                ]
            ]
        ]
        
        let response = try await callVisionAPI(requestBody: requestBody)
        
        // Validate response structure
        guard let json = try JSONSerialization.jsonObject(with: response.data(using: .utf8)!) as? [String: Any],
              let _ = json["responses"] as? [[String: Any]] else {
            throw TestError.invalidResponse("Invalid Vision API response structure")
        }
        
        print("   ✓ Google Vision API responded successfully")
        print("   ✓ Response contains valid structure")
        print("   ✓ API key authentication: ✓")
    }
    
    // Test end-to-end flow simulation
    private func testEndToEndFlow() async throws {
        print("   📸 Simulating image capture...")
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 second delay
        
        print("   🔍 Simulating ingredient extraction...")
        let mockIngredients = ["tomatoes", "onions", "garlic", "pasta"]
        
        print("   🍳 Generating recipes from extracted ingredients...")
        let recipePrompt = """
        Create 1 quick recipe using: \(mockIngredients.joined(separator: ", ")).
        Return JSON: [{"title": "Recipe Name", "description": "Quick description", "ingredients": \(mockIngredients), "instructions": ["Step 1", "Step 2"], "nutrition": {"calories": 350, "protein": 12, "carbs": 60, "fat": 8}, "cookingTime": 20, "servings": 2, "difficulty": "easy"}]
        """
        
        let recipeResponse = try await callGeminiAPI(prompt: recipePrompt)
        
        guard recipeResponse.contains("title") && recipeResponse.contains("ingredients") else {
            throw TestError.invalidResponse("Recipe generation failed")
        }
        
        print("   ✓ End-to-end flow completed successfully")
        print("   ✓ Image → Ingredients → Recipe: ✓")
    }
    
    // Helper: Call Gemini API
    private func callGeminiAPI(prompt: String) async throws -> String {
        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/publishers/google/models/gemini-2.0-flash:generateContent?key=\(geminiAPIKey)")!
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topP": 0.95,
                "maxOutputTokens": 1024
            ]
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw TestError.apiError("Gemini API request failed")
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw TestError.invalidResponse("Invalid Gemini API response format")
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // Helper: Call Vision API
    private func callVisionAPI(requestBody: [String: Any]) async throws -> String {
        let url = URL(string: "https://vision.googleapis.com/v1/images:annotate?key=\(visionAPIKey)")!
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw TestError.apiError("Vision API request failed")
        }
        
        return String(data: data, encoding: .utf8) ?? ""
    }
}

enum TestError: Error, LocalizedError {
    case invalidResponse(String)
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse(let message):
            return "Invalid response: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        }
    }
}

// String extension for repeat
extension String {
    static func * (string: String, count: Int) -> String {
        return String(repeating: string, count: count)
    }
}

// Run the integration tests
Task {
    let tester = IntegrationTest()
    await tester.runTests()
}

// Keep the script running
RunLoop.main.run()
