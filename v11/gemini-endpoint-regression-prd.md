# V11 Gemini 2.0 Flash Migration PRD
*Reference: https://cloud.google.com/vertex-ai/generative-ai/docs/migrate*

## Document Control
- Owner: iOS Platform Team (AI Integrations)
- Reviewers: Mobile Infra, Product Ops, Compliance
- Version: 2.0
- Last Updated: 2025-01-27
- Status: **CRITICAL - Production Outage Fix**

## Background
After shipping the Meal Plan refresh work, every Gemini-backed feature (Quick recipes, structured Meal Plan, recipe detail enrichment, image-to-text extraction) began failing with `HTTP 404` responses. Both production and staging environments reproduce the issue regardless of network stability or client-side retries. Investigations determined that the Gemini model URL was hard-coded to `models/gemini-1.5-flash-latest:generateContent`, an alias that Google no longer serves now that Gemini 1.5 Flash is in retirement. Google’s migration guidance for Vertex AI requires clients to move to the latest Gemini model families (for Flash workloads that is `gemini-2.0-flash`) and to reference the fully qualified publisher path. Because multiple services repeated the raw path, every call funneled into the missing alias without fallback, causing a systemic regression and leaving us out of compliance with the new model lifecycle.

Gemini 2.0 Flash introduces token-based pricing, adds built-in tool use, and shares the 1M token context window we relied on with 1.5 Flash. Google will retire Gemini 1.5 models on September 24, 2025, so this migration is both a resiliency fix and a deadline-driven upgrade. The new models also remove support for tuning certain sampling parameters (for example, `topK`), and features like Dynamic Retrieval now require the newer Gen AI SDK surface. This PRD captures the scope of the migration so the iOS app aligns with Google’s published guidance.

## Problem Statements
1. **Invalid model alias**: Gemini requests target a decommissioned alias, resulting in `HTTP 404` for all entry points.
2. **Fragmented configuration**: Services reconstruct the full model URL independently, making updates brittle and error-prone.
3. **No resiliency**: The client does not attempt secondary models, obscures server response bodies, and surfaces raw status codes to users.
4. **Outdated model contract**: The app assumes Gemini 1.5 Flash semantics (character-based billing, adjustable `topK`, Vertex AI SDK surfaces) that diverge from Gemini 2.0 Flash guidance, risking further breakage even after the alias fix.

## Goals
- Adopt `publishers/google/models/gemini-2.0-flash` as the primary model with a verified fallback chain that stays within Google’s retirement timelines.
- Centralize Gemini model configuration with support for primary and fallback model IDs.
- Ensure all Gemini consumers route through a single service-layer API that respects configuration and retry policy.
- Improve logging, error mapping, and user messaging so outages become diagnosable without crash reports.
- Align request options, pricing instrumentation, and SDK usage with the Gemini 2.x migration guide so future rotations are configuration-only.

## Non-Goals
- Modifying backend Gemini proxies or quota configurations.
- Redesigning Quick or Meal Plan prompts beyond routing through the new API.
- Implementing UI toggles for model switching outside developer/debug builds.

## Scope
### In Scope
- `Services/GeminiAPIService.swift`
- `Services/RecipeGenerationService.swift`
- All call sites depending on Gemini for content generation or vision tasks.
- Configuration loaders (`Application/api-keys.plist`, `Utilities/APIKeys.swift`) required to supply model IDs.
- Sampling and pricing instrumentation that must drop unsupported parameters (for example `topK`) and log token usage.
### Out of Scope
- Android or backend implementations.
- Billing plan changes or experimentation with new Gemini capabilities.
- User-visible feature work unrelated to the regression.

## Success Metrics
- 0% Gemini request failures attributable to invalid model alias when primary or fallback is available.
- Fallback success rate ≥ 95% when primary model returns `404` or `410`.
- Logs include model ID, status code, and truncated response body for ≥ 99% Gemini failures (captured via telemetry).
- Analytics dashboards confirm `gemini-2.0-flash` (or explicit fallback IDs) as the only model identifiers and report token-consumption metrics for 100% of calls.
- QA verifies Quick, Meal Plan, recipe detail, and image recognition flows complete with valid content using production keys.

## Functional Requirements
### FR-0 Critical Model Migration (Google Official Requirements)
- **FR-0.1** **IMMEDIATE**: Update all hardcoded URLs from `models/gemini-1.5-flash-latest` to `publishers/google/models/gemini-2.0-flash`
- **FR-0.2** **IMMEDIATE**: Remove `topK` parameter from all generation configs (not supported in Gemini 2.0+)
- **FR-0.3** Implement configurable fallback to `publishers/google/models/gemini-2.0-flash-lite` only (no 1.5 models)
- **FR-0.4** Replace character-based analytics with token-based usage metrics (prompt tokens + candidate tokens)

### FR-1 Configurable Model Routing
- **FR-1.1** Define `GeminiConfig` supporting `primaryModelID`, `fallbackModelIDs`, and request-scoped overrides.
- **FR-1.2** Load `GeminiConfig` from `api-keys.plist` (prod) with compile-time defaults for unit tests.
- **FR-1.3** Expose a `GeminiAPIService.generateContent(request:context:)` method that derives the full endpoint path from configuration.

### FR-2 Unified Service Surface
- **FR-2.1** Update `RecipeGenerationService` to call `GeminiAPIService` without reconstructing URLs locally.
- **FR-2.2** Migrate Quick recipes, Meal Plan, recipe detail, and image recognition entry points to the unified API.
- **FR-2.3** Remove duplicated constants and ensure no other module concatenates the model URL manually.

### FR-3 Resiliency & Telemetry
- **FR-3.1** When the primary model returns `404` or `410`, automatically retry once per fallback model (respecting rate limits).
- **FR-3.2** Log structured events with model ID, status, request fingerprint, and truncated response body for non-2xx responses.
- **FR-3.3** Map common Gemini errors to localized user messages; default to "Service temporarily unavailable" for unknown cases.
- **FR-3.4** Surface a developer diagnostic summary in debug builds (e.g., overlay or console) while preserving user-friendly messaging.

### FR-4 Testing & Validation
- **FR-4.1** Add unit tests validating primary/fallback sequencing and ensuring the URL is generated from configuration.
- **FR-4.2** Introduce integration tests using `URLProtocol` to simulate 404 → fallback success and verify telemetry output.
- **FR-4.3** Validate that a missing configuration fails fast with a descriptive error in development builds.

## Non-Functional Requirements
- Maintain Swift Concurrency compliance (actors, `async` APIs) and thread safety inside `GeminiAPIService`.
- Preserve performance budget: additional retry adds ≤ 300ms median latency when fallback is used.
- Do not store API keys or model IDs in logs; redact secrets before emitting telemetry.
- Ensure the solution supports future model rotations with configuration-only changes.
- Keep request payloads compatible with Gemini 2.x (omit deprecated parameters, respect token-based quotas).

## Implementation Guidance
- Keep `GeminiAPIService` as an `actor` to avoid shared-state races during fallback attempts.
- Inject `URLSession` or transport dependencies to enable deterministic tests.
- Encapsulate request construction so higher layers pass semantic parameters (prompt, mime types) rather than raw URLs.
- Provide helper methods for specialized flows (vision vs text) that reuse shared plumbing.
- Audit the current Vertex AI SDK usage; prefer the Google Gen AI Swift SDK surfaces when available, or document why we remain on custom HTTP so future upgrades are clearer.
- Normalize sampling configuration across call sites so unsupported parameters (for example `topK`) are not set for Gemini 2.x models.

## Milestones & Timeline
1. **Day 1 – Configuration & Service Refactor**
   - Introduce `GeminiConfig`, update loading logic with `gemini-2.0-flash` defaults, refactor `GeminiAPIService` surface.
2. **Day 2 – Consumer Migration & Fallback Logic**
   - Update all call sites, implement fallback and logging, remove disallowed sampling parameters, adjust error mapping.
3. **Day 3 – Testing & Validation**
   - Land unit/integration tests, execute manual QA scripts across affected flows, finalize telemetry dashboards with token usage metrics.

## Risks & Mitigations
- **Risk**: Fallback model returns schema variations, breaking downstream parsing.
  - **Mitigation**: Validate response schema; add defensive parsing or model-specific adapters.
- **Risk**: Increased latency from retries.
  - **Mitigation**: Cap retries to one per fallback and collect metrics to adjust configuration.
- **Risk**: Configuration drift between environments.
  - **Mitigation**: Centralize config file updates and add lint/check tooling in CI.
- **Risk**: Token-based billing and quotas behave differently from character-based tracking.
  - **Mitigation**: Update analytics and alerting to use token counts; dry run pricing dashboards before rollout.
- **Risk**: Existing SDK helpers rely on parameters (`topK`, Vertex AI SDK features) that Gemini 2.x no longer supports.
  - **Mitigation**: Surface compile-time assertions and code owners review to prevent setting unsupported options; document SDK decisions.

## Acceptance Criteria
- All Gemini flows succeed end-to-end with updated configuration in staging and production.
- Fallback attempt triggers automatically on simulated 404 and resolves successfully without user-visible error.
- New logging appears in observability dashboards with correct correlation identifiers.
- Telemetry shows only `gemini-2.0-flash` (and declared fallbacks) as the active model IDs with valid token counts.
- Sampling/SDK options in the app do not rely on deprecated Gemini 1.x-only parameters.
- PR merged with tests passing in CI and signed off by product & QA.

## Implementation Priority

### IMMEDIATE (Hours 1-2) - Production Fix
1. Update hardcoded URLs to `publishers/google/models/gemini-2.0-flash`
2. Remove `topK` parameter from all requests
3. Test basic functionality restoration

### SHORT TERM (Days 1-3) - Resilience
1. Implement `GeminiConfig` with fallback support
2. Add structured error logging and token analytics
3. Create comprehensive test coverage

### MEDIUM TERM (Days 4-7) - Optimization
1. Evaluate Gen AI SDK migration vs custom HTTP
2. Implement advanced monitoring and alerting
3. Add debug menu model information if needed

## Decision Points
- **SDK Migration**: Defer Gen AI SDK migration to focus on immediate fix
- **Remote Configuration**: Not needed for initial fix, evaluate later
- **Debug Menu**: Add model info display in debug builds only
