#!/usr/bin/env swift

import Foundation

// Debug test to check actual API responses
struct DebugTest {
    private let geminiAPIKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    private let visionAPIKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    
    func runDebugTests() async {
        print("🔍 Debug Tests - Checking API Responses")
        print("=" * 50)
        
        // Test Gemini API response format
        await testGeminiResponse()
        
        // Test Vision API response format
        await testVisionResponse()
    }
    
    private func testGeminiResponse() async {
        print("\n📝 Testing Gemini API Response Format...")
        
        do {
            let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/publishers/google/models/gemini-2.0-flash:generateContent?key=\(geminiAPIKey)")!
            
            let requestBody: [String: Any] = [
                "contents": [
                    [
                        "parts": [
                            ["text": "Say 'Hello World' and nothing else."]
                        ]
                    ]
                ]
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            request.timeoutInterval = 30.0
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("   Status Code: \(httpResponse.statusCode)")
            }
            
            let responseString = String(data: data, encoding: .utf8) ?? "Unable to decode response"
            print("   Raw Response:")
            print("   \(responseString)")
            
            // Try to parse JSON
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("   ✓ Valid JSON response")
                
                if let candidates = json["candidates"] as? [[String: Any]] {
                    print("   ✓ Found candidates array with \(candidates.count) items")
                    
                    if let firstCandidate = candidates.first {
                        print("   ✓ First candidate keys: \(Array(firstCandidate.keys))")
                        
                        if let content = firstCandidate["content"] as? [String: Any] {
                            print("   ✓ Found content object with keys: \(Array(content.keys))")
                            
                            if let parts = content["parts"] as? [[String: Any]] {
                                print("   ✓ Found parts array with \(parts.count) items")
                                
                                if let firstPart = parts.first,
                                   let text = firstPart["text"] as? String {
                                    print("   ✓ Extracted text: '\(text)'")
                                    print("   ✅ Gemini API working correctly!")
                                } else {
                                    print("   ❌ Could not extract text from first part")
                                }
                            } else {
                                print("   ❌ No parts array found")
                            }
                        } else {
                            print("   ❌ No content object found")
                        }
                    } else {
                        print("   ❌ No first candidate found")
                    }
                } else {
                    print("   ❌ No candidates array found")
                }
            } else {
                print("   ❌ Invalid JSON response")
            }
            
        } catch {
            print("   ❌ Error: \(error)")
        }
    }
    
    private func testVisionResponse() async {
        print("\n👁️ Testing Vision API Response Format...")
        
        do {
            let url = URL(string: "https://vision.googleapis.com/v1/images:annotate?key=\(visionAPIKey)")!
            
            // Simple 1x1 white pixel image
            let testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            
            let requestBody: [String: Any] = [
                "requests": [
                    [
                        "image": [
                            "content": testImageBase64
                        ],
                        "features": [
                            ["type": "LABEL_DETECTION", "maxResults": 5]
                        ]
                    ]
                ]
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            request.timeoutInterval = 30.0
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("   Status Code: \(httpResponse.statusCode)")
            }
            
            let responseString = String(data: data, encoding: .utf8) ?? "Unable to decode response"
            print("   Raw Response:")
            print("   \(responseString)")
            
            // Try to parse JSON
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("   ✓ Valid JSON response")
                
                if let responses = json["responses"] as? [[String: Any]] {
                    print("   ✓ Found responses array with \(responses.count) items")
                    print("   ✅ Vision API working correctly!")
                } else {
                    print("   ❌ No responses array found")
                }
            } else {
                print("   ❌ Invalid JSON response")
            }
            
        } catch {
            print("   ❌ Error: \(error)")
        }
    }
}

// String extension for repeat
extension String {
    static func * (string: String, count: Int) -> String {
        return String(repeating: string, count: count)
    }
}

// Run the debug tests
Task {
    let debugger = DebugTest()
    await debugger.runDebugTests()
}

// Keep the script running
RunLoop.main.run()
